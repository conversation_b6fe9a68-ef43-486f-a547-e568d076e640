<template>
  <div class="environmental-alarm-page">
    <div :class="'main-tree'" v-if="isMenu" style="background-color:rgba(11, 32, 87, 0.6);z-index: 99998">
      <menuDialog></menuDialog>
    </div>
    <!-- 菜单图标 -->
    <div
        class="draggable-menu"
        ref="menu"
        :style="{ left: `${x}px`, top: `${y}px`, 'z-index': 99999 }"
        @mousedown="startDrag"
        @click="iconHandle"
        :class="{ active: isDragging }"
    >
      <div class="menu-icon">
        <i class="el-icon-menu"></i>
      </div>
    </div>
    <div :class="['main-body-s', isCollapsed && !isMenu ? 'main-body-s' : '']">
      <el-row>
        <el-col :span="12">
          <div class="chart-container">
            <img
                src="@/assets/statistics/pic1.png"
                alt="统计"
                style="width:70%; height: 60%;z-index: -1; object-fit: contain; "
            />
            <!--LDAR-->
            <a href="http://139.224.246.62/permission/login" target="_blank">
              <div style=" position: absolute;width: 120px;height: 120px; top:80px; left: 390px;">
              </div>
            </a>
            <!--即时通讯-->
            <div
                style=" position: absolute;width: 120px;height: 120px; top:160px; left: 0;"
                @click="jumpHandle('/environmental/backlogResult')"
            ></div>
            <!--无异味企业-->
            <div
                style=" position: absolute;width: 120px;height: 120px; top:0px; left: 200px;"
                @click="jumpHandle('/environmental/company')"
            ></div>
          </div>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span @click="jumpHandle('/environmental/overview')">污染源分布图</span>
              <div>
                <span style="color: #fff;margin-right: 5px">日期:</span>
                <el-date-picker v-model="environmentalData.day" type="date" placeholder="选择日期">
                </el-date-picker>
                <el-button type="primary" @click="queryHandle()" style="margin-left: 5px"
                >查询
                </el-button
                >
              </div>
            </div>
            <div class="content-jy">
              <column-chart1 :chart-data="columnDate" :height="180"></column-chart1>
            </div>
          </el-card>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span @click="jumpHandle('/environmental/alarmQuery')">在线监测</span>
              <div>
                <span style="color: #fff;margin-right: 5px">日期:</span>
                <el-date-picker v-model="environmentalData.day2" type="date" placeholder="选择日期">
                </el-date-picker>
                <el-button type="primary" @click="queryHandle1()" style="margin-left: 5px"
                >查询
                </el-button
                >
              </div>
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <column-chart2 :chart-data="columnADate" :title="''"></column-chart2>
              </el-col>
              <el-col :span="6">
                <div style="text-align: center">
                  <el-progress type="circle" :percentage="pieWDate" :color="colors"></el-progress>
                  <br/>
                  <div style="margin-top: 2px">废水超标已处理</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div style="text-align: center">
                  <el-progress type="circle" :percentage="pieADate" :color="colors"></el-progress>
                  <br/>
                  <div style="margin-top: 2px">废气超标已处理</div>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span @click="jumpHandle('/environmental/monitor')">自行监测</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="status-card" style="width: 100%;height: 110px">
                  <div
                      style="color:#fff;font-size: 28px;font-weight:400;
                  text-align: center;line-height: 56px;"
                  >
                    {{ data.year }}年
                  </div>
                </div>
              </el-col>
              <el-col :span="18">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="status-card1" style="width: 100%;height: 50px">
                      <el-row :gutter="20">
                        <el-col :span="8" style="text-align: right">
                          <img
                              src="@/assets/hbgk-imgs/image4.png"
                              style="width: 30px;height: 30px;margin-top: 10px"
                          />
                        </el-col>
                        <el-col :span="16">
                          <div
                              style="font-size: 13px; color: #FFFFFF;font-weight: 700;margin-top: 10px;text-align: center"
                          >
                            自行监测方案数量
                            <br/>
                            <span
                                style="color: #00CCFF;font-size: 11px;font-weight: 700;margin-top: 10px"
                            >
                                                            {{ data.summary }}
                                                        </span>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="status-card1" style="width: 100%;height: 50px">
                      <el-row :gutter="20">
                        <el-col :span="8" style="text-align: right">
                          <img
                              src="@/assets/hbgk-imgs/image1.png"
                              style="width: 30px;height: 30px;margin-top: 10px"
                          />
                        </el-col>
                        <el-col :span="16">
                          <div
                              style="font-size: 13px; color: #FFFFFF;font-weight: 700;margin-top: 10px;text-align: center"
                          >
                            进行中监测方案
                            <br/>
                            <span
                                style="color: #00CCFF;font-size: 11px;font-weight: 700;margin-top: 10px"
                            >
                                                            {{ data.count1 }}
                                                        </span>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20" style="margin-top: 10px">
                  <el-col :span="12">
                    <div class="status-card1" style="width: 100%;height: 50px">
                      <el-row :gutter="20">
                        <el-col :span="8" style="text-align: right">
                          <img
                              src="@/assets/hbgk-imgs/image3.png"
                              style="width: 30px;height: 30px;margin-top: 10px"
                          />
                        </el-col>
                        <el-col :span="16">
                          <div
                              style="font-size: 13px; color: #FFFFFF;font-weight: 700;margin-top: 10px;text-align: center"
                          >
                            未完成监测方案
                            <br/>
                            <span
                                style="color: #00CCFF;font-size: 11px;font-weight: 700;margin-top: 10px"
                            >
                                                            {{ data.count2 }}
                                                        </span>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="status-card1" style="width: 100%;height: 50px">
                      <el-row :gutter="20">
                        <el-col :span="8" style="text-align: right">
                          <img
                              src="@/assets/hbgk-imgs/image2.png"
                              style="width: 30px;height: 30px;margin-top: 10px"
                          />
                        </el-col>
                        <el-col :span="16">
                          <div
                              style="font-size: 13px; color: #FFFFFF;font-weight: 700;margin-top: 10px;text-align: center"
                          >
                            已完成监测方案
                            <br/>
                            <span
                                style="color: #00CCFF;font-size: 11px;font-weight: 700;margin-top: 10px"
                            >
                                                            {{ data.count3 }}
                                                        </span>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import dayjs from "dayjs";
import menuDialog from "@/components/menu.vue";
import ColumnChart1 from "./components/ColumnChart1.vue";
import ColumnChart2 from "./components/ColumnChart2.vue";
import {getMonitorStatistics} from "@/http/environmental-overview";
import {monitorResultChildCount} from "@/http/monitor";
import {overMonitorColumnStatistics, overMonitorStatistics} from "@/http/alarm-query";

export default {
  components: {ColumnChart1, menuDialog, ColumnChart2},
  data() {
    return {
      colors: "red",
      x: 20, // 初始X位置
      y: 500, // 初始Y位置
      isDragging: false,
      isMenu: false,
      isCollapsed: true,
      environmentalData: {
        day: new Date(),
        day2: new Date()
      },
      data: {},
      columnDate: [],
      pieADate: 0,
      pieWDate: 0,
      columnADate: [],
      data1: 0,
      data2: 0,
      data3: 0,
      data4: 0
    };
  },
  methods: {
    jumpHandle(url) {
      this.$router.push({
        path: url
      });
    },
    jumpUrlHandle(url) {
      window.location.href = url;
    },
    iconHandle() {
      this.isMenu = !this.isMenu;
      this.isCollapsed = true;
    },
    startDrag(e) {
      this.isDragging = true;
      const menu = this.$refs.menu.getBoundingClientRect();

      const onMouseMove = (e) => {
        const x = e.clientX - menu.width / 2;
        const y = e.clientY - menu.height / 2;

        // 边界限制
        this.x = Math.max(0, Math.min(x, window.innerWidth - menu.width));
        this.y = Math.max(0, Math.min(y, window.innerHeight - menu.height));
      };

      const onMouseUp = () => {
        this.isDragging = false;
        document.removeEventListener("mousemove", onMouseMove);
        document.removeEventListener("mouseup", onMouseUp);
      };

      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("mouseup", onMouseUp);
    },
    // 查询
    queryHandle() {
      let date = "";
      if (this.environmentalData.day != null) {
        let year = this.environmentalData.day.getFullYear();
        let month =
            this.environmentalData.day.getMonth() + 1 < 10
                ? "0" + (this.environmentalData.day.getMonth() + 1)
                : this.environmentalData.day.getMonth() + 1;
        let day =
            this.environmentalData.day.getDate() < 10
                ? "0" + this.environmentalData.day.getDate()
                : this.environmentalData.day.getDate();
        date = year + "-" + month + "-" + day;
      }
      this.getMonitorStatistics(date);
    },
    async queryHandle1() {
      let date = "";
      if (this.environmentalData.day != null) {
        let year = this.environmentalData.day.getFullYear();
        let month =
            this.environmentalData.day.getMonth() + 1 < 10
                ? "0" + (this.environmentalData.day.getMonth() + 1)
                : this.environmentalData.day.getMonth() + 1;
        let day =
            this.environmentalData.day.getDate() < 10
                ? "0" + this.environmentalData.day.getDate()
                : this.environmentalData.day.getDate();
        date = year + "-" + month + "-" + day;
      }
      this.columnADate = [];
      await this.overMonitorStatistics(10);
      await this.overMonitorStatistics(20);
      console.log(" this.columnADate.", this.columnADate);
    },
    // 监测点统计
    getMonitorStatistics(date) {
      getMonitorStatistics({date: date}).then((res) => {
        this.columnDate = res.data;
      });
    },
    getMonitorResultChildCount() {
      monitorResultChildCount().then((res) => {
        this.data = res.data;
      });
    },
    // 超标统计
    async overMonitorStatistics(monitorType) {
      const form = {
        startTime: "",
        endTime: "",
        vldSiteId: "",
        monitorType: monitorType
      };
      if (this.environmentalData.day2) {
        form.startTime = dayjs(this.environmentalData.day2).format("YYYY-MM-DD 00:00:00");
        form.endTime = dayjs(this.environmentalData.day2).format("YYYY-MM-DD 23:59:59");
      }
      overMonitorStatistics(form).then((response) => {
        if (monitorType === 10) {
          let num1 = response.data[0].value;
          let num2 = response.data[1].value;
          if (num1 + num2 === 0) {
            this.pieWDate = 0;
          } else {
            this.pieWDate = ((num1 / (num1 + num2)) * 100).toFixed(2);
          }
        }
        if (monitorType === 20) {
          let num1 = response.data[0].value;
          let num2 = response.data[1].value;
          if (num1 + num2 === 0) {
            this.pieWDate = 0;
          } else {
            this.pieWDate = ((num1 / (num1 + num2)) * 100).toFixed(2);
          }
        }
      });

      overMonitorColumnStatistics(form).then((response) => {
        console.log(response);
        if (monitorType === 10) {
          let data1 = response.data[1];

          this.columnADate.push(data1[0]);
          this.columnADate.push(data1[1]);
        }
        if (monitorType === 20) {
          let data1 = response.data[1];
          this.columnADate.push(data1[0]);
          this.columnADate.push(data1[1]);
        }
      });
    }
  },
  created() {
    this.queryHandle();
    this.getMonitorResultChildCount();
    this.queryHandle1();
  }
};
</script>

<style lang="less" scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%; /* 根据实际需要调整 */
}

//---
.draggable-menu {
  position: fixed;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  cursor: move;
  transition: transform 0.2s ease;
  user-select: none;
}

.draggable-menu.active {
  transform: scale(1.02);
}

.menu-icon {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: rgba(0, 27, 64, 0.6);
  border-bottom: 1px solid #2a2a2a;
  font-weight: 500;
  color: #e0e0e0;
}

.menu-icon i {
  font-size: 50px;
}

.menu-content {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 400px;
}

.menu-item {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #c0c0c0;
  font-size: 14px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

//---
div::-webkit-scrollbar {
  display: none;
}

.main-tree {
  height: calc(100% - 85px);
  width: 390px;
  position: absolute;
  left: 0px;
  background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  ::v-deep .el-input--small {
    border: 1px solid #00409a;
    border-radius: 5px;
  }

  ::v-deep .el-input__inner {
    box-shadow: 0 0 0.05rem transparent inset;
    background: rgba(0, 27, 64, 0.6);
  }
}

.main-body {
  height: calc(100% - 85px);
  width: calc(100% - 420px);
  position: absolute;
  left: 430px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  .el-input__inner {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem #86a5e7 inset;
  }
}

.main-body-s {
  height: calc(100% - 85px);
  width: 100%;
  position: absolute;
  left: 25px;
  //background: rgba(0, 43, 104, 0.4);
  padding: 24px;
  transition: left 0.5s;

  .el-input__inner {
    color: #fff;
    background-color: #073aa9;
    border: 0;
    box-shadow: 0 0 0.05rem #86a5e7 inset;
  }
}

.environmental-alarm-page {
  //background: url("~@/assets/hbgk-imgs/img.png");
  width: 100%;
  height: 100%;
  color: #ffffff;
  display: block;
}

::v-deep .el-card {
  background: transparent;
  border: 1px solid rgb(43 141 197);
  color: #ffffff;
  width: 100%;
  margin-top: 10px;

  .el-card__header {
    border-color: rgb(43 141 197);
    background-color: rgba(7, 58, 169, 0.3);

    .clearfix:after {
      display: none;
    }

    .clearfix {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span:nth-child(1) {
        font-size: 14px;
      }

      span:nth-child(2) {
        font-size: 12px;
      }
    }
  }

  .el-card__body {
    font-size: 12px;
    background-color: rgba(7, 58, 169, 0);
  }
}

.select-jy {
  position: static;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.select-jy-jy {
  justify-content: flex-start;
}

.content-jy {
  width: 100%;
  height: calc(100% - 51px);
  display: flex;

  #echartsJy {
    margin-right: 16px;
  }

  div {
    width: calc(50% - 8px);
  }
}

::v-deep .el-form-item__label {
  color: #ffffff;
}

::v-deep .el-input__inner {
  color: #fff;
  background-color: #073aa9;
  border: 0;
  box-shadow: 0 0 0.05rem #86a5e7 inset;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.year {
  margin: 5px 0 20px;
  font-size: 16px;
  opacity: 0.8;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.status-card {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
}

.status-card1 {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.status-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-label {
  font-size: 14px;
}

/* 不同状态的样式 */
.completed .status-icon {
  background-color: #1976d2; /* 蓝色 */
}

.in-progress .status-icon {
  background-color: #4caf50; /* 绿色 */
}

.not-completed .status-icon {
  background-color: #8bc34a; /* 浅绿色 */
}

.pending .status-icon {
  background-color: #0d47a1; /* 深蓝色 */
}

:deep .el-progress__text {
  color: #fff;
}
</style>
