<template>
  <div ref="chart" class="chart-container"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    timeData: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
    height: {
      type: [Number, String],  // 支持数字或字符串形式（如"100%"）
      default: '100%'
    }
  },
  watch: {
    chartData(newVal, oldVal) {
      this.initChart()
    }
  },
  name: 'LineChart',
  mounted() {
    this.initChart();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    initChart() {
      // 销毁旧实例
      if (this.myChart) {
        this.myChart.dispose();
      }

      // 初始化图表
      const chartDom = this.$refs.chart;
      this.myChart = echarts.init(chartDom);

      // 配置项
      const option = {
        title: {
          text: this.title,
          textStyle: {
            color: "#fff",
          },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: this.title ? '15%' : '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            color: "#99b7d2",
          },
        },
        yAxis: {
          type: 'category',
          data: ["COD超标","氨氮超标","SO2超标","NOX超标"],
          axisLabel: {
            color: "#99b7d2",
          },
        },
        series: [
          {
            name: '超标',
            type: 'bar',
            data: this.chartData,
            itemStyle: {
              color: 'rgba(83, 240, 233, 0.8)',
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'right',
              color: '#fff'
            }
          },
        ]
      };

      this.myChart.setOption(option);
      this.handleResize(); // 初始调整大小
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
  }
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: v-bind(height);
  min-height: 180px;
}
</style>