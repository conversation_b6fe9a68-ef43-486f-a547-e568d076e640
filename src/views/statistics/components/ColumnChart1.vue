<template>
  <div ref="chart" :style="'width: 100%; height: 100%;'"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    timeData: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    }
  },
  watch: {
    chartData(newVal, oldVal) {
      this.initChart();
    }
  },
  name: 'LineC<PERSON>',
  mounted() {
    console.log('Component mounted.')
    this.initChart();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    console.log('Component beforeDestroy.')
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      const chartDom = this.$refs.chart;
      const myChart = echarts.init(chartDom);

      const option = {
        backgroundColor: 'transparent',
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: this.chartData[0],
          axisLabel: {
            color: "#99b7d2",
            interval: 0,
            rotate: 25,
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#2a3f6f'
            }
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          interval: 25,
          axisLabel: {
            color: "#99b7d2",
            formatter: function(value) {
              return value.toFixed(1);
            }
          },
          axisLine: {
            lineStyle: {
              color: '#2a3f6f'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#2a3f6f',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '废气超标量',
            type: 'bar',
            data: this.chartData[1],
            itemStyle: {
              color: 'rgba(83, 240, 233, 0.8)',
              borderRadius: [2, 2, 0, 0]
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            }
          },
          {
            name: '废水超标量',
            type: 'bar',
            data: this.chartData[2],
            itemStyle: {
              color: 'rgba(255, 183, 77, 0.8)',
              borderRadius: [2, 2, 0, 0]
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              color: '#fff'
            }
          }
        ]
      };

      myChart.setOption(option);
    },
    handleResize() {
      if (this.$refs.chart && this.$refs.chart.style) {
        this.$refs.chart.style.width = '100%';
        this.$refs.chart.style.height = '100%';
      }
      if (this.$refs.chart && this.$refs.chart.echartsInstance) {
        this.$refs.chart.echartsInstance.resize();
      }
    }
  }
};
</script>

<style scoped>
/* 确保图表容器填满父容器 */
div {
  width: 100%;
  height: 100%;
  min-height: 150px; /* 设置最小高度防止内容过小 */
}
</style>