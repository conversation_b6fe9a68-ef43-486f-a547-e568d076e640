// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core';

export {};

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb'];
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem'];
    AButton: typeof import('ant-design-vue/es')['Button'];
    ACard: typeof import('ant-design-vue/es')['Card'];
    ACol: typeof import('ant-design-vue/es')['Col'];
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider'];
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker'];
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions'];
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem'];
    ADivider: typeof import('ant-design-vue/es')['Divider'];
    ADropdown: typeof import('ant-design-vue/es')['Dropdown'];
    AEmpty: typeof import('ant-design-vue/es')['Empty'];
    AForm: typeof import('ant-design-vue/es')['Form'];
    AFormItem: typeof import('ant-design-vue/es')['FormItem'];
    AInput: typeof import('ant-design-vue/es')['Input'];
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber'];
    ALayout: typeof import('ant-design-vue/es')['Layout'];
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent'];
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader'];
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider'];
    AMenu: typeof import('ant-design-vue/es')['Menu'];
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem'];
    AModal: typeof import('ant-design-vue/es')['Modal'];
    APagination: typeof import('ant-design-vue/es')['Pagination'];
    APopover: typeof import('ant-design-vue/es')['Popover'];
    ARadio: typeof import('ant-design-vue/es')['Radio'];
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup'];
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker'];
    ARow: typeof import('ant-design-vue/es')['Row'];
    ASelect: typeof import('ant-design-vue/es')['Select'];
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption'];
    ASpace: typeof import('ant-design-vue/es')['Space'];
    ASpin: typeof import('ant-design-vue/es')['Spin'];
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu'];
    ASwitch: typeof import('ant-design-vue/es')['Switch'];
    ATable: typeof import('ant-design-vue/es')['Table'];
    ATableColumn: typeof import('ant-design-vue/es')['TableColumn'];
    ATabPane: typeof import('ant-design-vue/es')['TabPane'];
    ATabs: typeof import('ant-design-vue/es')['Tabs'];
    ATextarea: typeof import('ant-design-vue/es')['Textarea'];
    ATimePicker: typeof import('ant-design-vue/es')['TimePicker'];
    ATree: typeof import('ant-design-vue/es')['Tree'];
    AUpload: typeof import('ant-design-vue/es')['Upload'];
    Button: typeof import('./components/Button/index.vue')['default'];
    CardCollapse: typeof import('./components/card-collapse.vue')['default'];
    DetailsDialog: typeof import('./components/detailsDialog.vue')['default'];
    DvWrapper: typeof import('./components/DvWrapper.vue')['default'];
    EditTable: typeof import('./components/EditTable/index.vue')['default'];
    IconifyIcon: typeof import('./components/IconifyIcon/index.vue')['default'];
    Menu: typeof import('./components/menu.vue')['default'];
    MenuOutlined: typeof import('@ant-design/icons-vue')['MenuOutlined'];
    ProTable: typeof import('./components/ProTable/index.vue')['default'];
    Radio: typeof import('./components/Radio/index.vue')['default'];
    RouterLink: typeof import('vue-router')['RouterLink'];
    RouterView: typeof import('vue-router')['RouterView'];
    SearchOutlined: typeof import('@ant-design/icons-vue')['SearchOutlined'];
    TagsView: typeof import('./components/TagsView/index.vue')['default'];
    TreeDialog: typeof import('./components/treeDialog.vue')['default'];
  }
}
